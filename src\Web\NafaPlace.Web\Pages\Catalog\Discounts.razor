@page "/catalog/discounts"
@using NafaPlace.Web.Services
@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Components.Reviews
@inject IProductService ProductService
@inject ICategoryService CategoryService
@inject ICartService CartService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@using System.Security.Claims
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>Promotions - NafaPlace</PageTitle>

<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Accueil</a></li>
                    <li class="breadcrumb-item"><a href="/catalog">Catalogue</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Promotions</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center mb-3">
                <i class="bi bi-percent text-danger me-2" style="font-size: 2rem;"></i>
                <h1 class="mb-0">Promotions</h1>
            </div>
            <p class="text-muted">Profitez de nos offres spéciales et réductions exceptionnelles.</p>
        </div>
    </div>

    @if (loading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-2">Chargement des promotions...</p>
        </div>
    }
    else if (products?.Any() == true)
    {
        <div class="row">
            @foreach (var product in products)
            {
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="product-card h-100">
                        <div class="position-relative">
                            <img src="@GetProductImageUrl(product.ImageUrl)" 
                                 class="product-image" 
                                 alt="@product.Name"
                                 loading="lazy" />
                            @if (product.DiscountPrice.HasValue && product.DiscountPrice < product.Price)
                            {
                                var discountPercent = (int)Math.Round(((product.Price - product.DiscountPrice.Value) / product.Price) * 100);
                                <div class="product-badge bg-danger text-white">
                                    <i class="bi bi-percent me-1"></i>-@discountPercent%
                                </div>
                            }
                        </div>
                        
                        <div class="product-info">
                            <h5 class="product-title">@product.Name</h5>
                            <p class="product-description">@product.Description</p>
                            
                            <div class="product-rating mb-2">
                                <ReviewStars Rating="@product.AverageRating" />
                                <span class="text-muted ms-2">(@product.ReviewCount avis)</span>
                            </div>
                            
                            <div class="product-price">
                                @if (product.DiscountPrice.HasValue && product.DiscountPrice < product.Price)
                                {
                                    <span class="price-original">@product.Price.ToString("N0") GNF</span>
                                    <span class="price-discount">@product.DiscountPrice.Value.ToString("N0") GNF</span>
                                    <div class="savings-amount text-success mt-1">
                                        <small><i class="bi bi-piggy-bank me-1"></i>Économisez @((product.Price - product.DiscountPrice.Value).ToString("N0")) GNF</small>
                                    </div>
                                }
                                else
                                {
                                    <span class="price-current">@product.Price.ToString("N0") GNF</span>
                                }
                            </div>
                            
                            <div class="product-actions mt-3">
                                <button class="btn btn-primary btn-add-cart w-100" 
                                        @onclick="() => ViewProduct(product.Id)"
                                        disabled="@(product.Stock <= 0)">
                                    @if (product.Stock <= 0)
                                    {
                                        <i class="bi bi-x-circle me-2"></i>Rupture de stock
                                    }
                                    else
                                    {
                                        <i class="bi bi-eye me-2"></i>Voir le produit
                                    }
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="bi bi-percent text-muted" style="font-size: 4rem;"></i>
            <h3 class="mt-3 text-muted">Aucune promotion disponible</h3>
            <p class="text-muted">Revenez bientôt pour découvrir nos offres spéciales !</p>
            <a href="/catalog" class="btn btn-primary">
                <i class="bi bi-box-seam me-2"></i>Voir tous les produits
            </a>
        </div>
    }
</div>

@code {
    private List<ProductDto> products = new();
    private bool loading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadDiscountedProducts();
    }

    private async Task LoadDiscountedProducts()
    {
        try
        {
            loading = true;
            // Récupérer tous les produits et filtrer ceux qui ont des réductions
            var allProducts = await ProductService.GetAllProductsAsync();
            products = allProducts.Where(p => p.DiscountPrice.HasValue && p.DiscountPrice < p.Price).ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading discounted products: {ex.Message}");
        }
        finally
        {
            loading = false;
        }
    }

    private string GetProductImageUrl(string imageUrl)
    {
        if (string.IsNullOrEmpty(imageUrl))
            return "/images/placeholder.png";
        
        if (imageUrl.StartsWith("http"))
            return imageUrl;
        
        return $"/images/{imageUrl}";
    }

    private void ViewProduct(int productId)
    {
        NavigationManager.NavigateTo($"/catalog/product/{productId}");
    }
}
