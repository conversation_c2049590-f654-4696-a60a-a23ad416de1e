@page "/promotions"
@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Services
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject IProductService ProductService
@inject ICategoryService CategoryService
@inject ICartService CartService
@inject IJSRuntime JSRuntime
@inject NavigationManager NavigationManager
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>Promotions - NafaPlace</PageTitle>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="bi bi-percent text-danger me-3"></i>
                    Promotions
                </h1>
                <p class="page-subtitle">Profitez de nos meilleures offres et réductions exceptionnelles</p>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="filters-card">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="categoryFilter" class="form-label">Catégorie</label>
                            <select id="categoryFilter" class="form-select" @onchange="OnCategoryChanged">
                                <option value="">Toutes les catégories</option>
                                @if (categories != null)
                                {
                                    @foreach (var category in categories)
                                    {
                                        <option value="@category.Id">@category.Name</option>
                                    }
                                }
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="discountFilter" class="form-label">Réduction minimum</label>
                            <select id="discountFilter" class="form-select" @onchange="OnDiscountChanged">
                                <option value="0">Toutes les réductions</option>
                                <option value="10">10% et plus</option>
                                <option value="20">20% et plus</option>
                                <option value="30">30% et plus</option>
                                <option value="50">50% et plus</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="sortFilter" class="form-label">Trier par</label>
                            <select id="sortFilter" class="form-select" @onchange="OnSortChanged">
                                <option value="discount-desc">Réduction décroissante</option>
                                <option value="discount-asc">Réduction croissante</option>
                                <option value="price-asc">Prix croissant</option>
                                <option value="price-desc">Prix décroissant</option>
                                <option value="name">Nom A-Z</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="itemsPerPage" class="form-label">Produits par page</label>
                            <select id="itemsPerPage" class="form-select" @onchange="OnPageSizeChanged">
                                <option value="12">12 produits</option>
                                <option value="24">24 produits</option>
                                <option value="48">48 produits</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Promotion Stats -->
    @if (products != null && products.Any())
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="promotion-stats">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h3 class="stat-number">@products.Count()</h3>
                                <p class="stat-label">Produits en promotion</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h3 class="stat-number">@(products.Any() ? products.Max(p => p.DiscountPercentage) : 0)%</h3>
                                <p class="stat-label">Réduction maximale</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h3 class="stat-number">@(products.Any() ? Math.Round(products.Average(p => p.DiscountPercentage), 1) : 0)%</h3>
                                <p class="stat-label">Réduction moyenne</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h3 class="stat-number">@(products.Any() && products.Any(p => p.OldPrice.HasValue) ? products.Where(p => p.OldPrice.HasValue).Sum(p => p.OldPrice!.Value - p.Price).ToString("N0") : "0") GNF</h3>
                                <p class="stat-label">Économies totales</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Products Grid -->
    <div class="row">
        <div class="col-12">
            @if (isLoading)
            {
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-3">Chargement des promotions...</p>
                </div>
            }
            else if (products == null || !products.Any())
            {
                <div class="empty-state text-center py-5">
                    <i class="bi bi-percent display-1 text-muted"></i>
                    <h3 class="mt-3">Aucune promotion trouvée</h3>
                    <p class="text-muted">Aucun produit en promotion ne correspond à vos critères.</p>
                    <a href="/catalog" class="btn btn-primary">
                        <i class="bi bi-grid me-2"></i>Voir tous les produits
                    </a>
                </div>
            }
            else
            {
                <div class="row g-4">
                    @foreach (var product in products)
                    {
                        <div class="col-6 col-md-3">
                            <div class="card product-card h-100">
                                <!-- Discount Badge -->
                                <span class="badge badge-sale">-@product.DiscountPercentage%</span>

                                <a href="/catalog/products/@product.Id">
                                    <img src="@(product.Images.Any() ? ProductService.GetImageUrl(product.Images.First()) : "/images/placeholder.png")" 
                                         class="card-img-top" alt="@product.Name">
                                </a>

                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title">@product.Name</h6>
                                    <p class="card-text text-muted small">@product.Category?.Name</p>
                                    
                                    <div class="price-section mt-auto">
                                        @if (product.OldPrice.HasValue)
                                        {
                                            <div class="price-with-discount">
                                                <span class="current-price">@product.Price.ToString("N0") GNF</span>
                                                <span class="original-price">@product.OldPrice.Value.ToString("N0") GNF</span>
                                            </div>
                                            <div class="savings-amount">
                                                <small class="text-success">
                                                    <i class="bi bi-arrow-down"></i>
                                                    Économisez @((product.OldPrice.Value - product.Price).ToString("N0")) GNF
                                                </small>
                                            </div>
                                        }
                                        else
                                        {
                                            <span class="current-price">@product.Price.ToString("N0") GNF</span>
                                        }
                                    </div>

                                    <div class="card-actions mt-3">
                                        <button class="btn btn-primary btn-sm w-100" @onclick="() => AddToCart(product.Id)">
                                            <i class="bi bi-cart-plus me-1"></i>Ajouter
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <!-- Pagination -->
                @if (totalPages > 1)
                {
                    <nav aria-label="Navigation des promotions" class="mt-5">
                        <ul class="pagination justify-content-center">
                            <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                                <button class="page-link" @onclick="() => ChangePage(currentPage - 1)" disabled="@(currentPage == 1)">
                                    <i class="bi bi-chevron-left"></i>
                                </button>
                            </li>
                            
                            @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                            {
                                <li class="page-item @(i == currentPage ? "active" : "")">
                                    <button class="page-link" @onclick="() => ChangePage(i)">@i</button>
                                </li>
                            }
                            
                            <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                                <button class="page-link" @onclick="() => ChangePage(currentPage + 1)" disabled="@(currentPage == totalPages)">
                                    <i class="bi bi-chevron-right"></i>
                                </button>
                            </li>
                        </ul>
                    </nav>
                }
            }
        </div>
    </div>
</div>

@code {
    private IEnumerable<ProductDto>? products;
    private IEnumerable<CategoryDto>? categories;
    private bool isLoading = true;
    private int currentPage = 1;
    private int pageSize = 12;
    private int totalPages = 1;
    private int totalItems = 0;
    private int? selectedCategoryId;
    private int minDiscountPercentage = 0;
    private string sortBy = "discount-desc";
    private string? _userId;

    [CascadingParameter]
    public Task<AuthenticationState> AuthenticationStateTask { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        // Récupérer l'utilisateur connecté
        var authState = await AuthenticationStateTask;
        var user = authState.User;
        if (user.Identity?.IsAuthenticated == true)
        {
            _userId = user.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
        }

        await LoadCategories();
        await LoadProducts();
    }

    private async Task LoadCategories()
    {
        try
        {
            categories = await CategoryService.GetAllCategoriesAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading categories: {ex.Message}");
        }
    }

    private async Task LoadProducts()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            // Récupérer tous les produits et filtrer ceux en promotion
            var allProducts = await ProductService.GetFeaturedProductsAsync(100);
            
            // Filtrer uniquement les produits en promotion
            var promotionProducts = allProducts.Where(p => p.DiscountPercentage > 0);
            
            // Filtrer par catégorie si sélectionnée
            if (selectedCategoryId.HasValue)
            {
                promotionProducts = promotionProducts.Where(p => p.CategoryId == selectedCategoryId.Value);
            }

            // Filtrer par pourcentage de réduction minimum
            if (minDiscountPercentage > 0)
            {
                promotionProducts = promotionProducts.Where(p => p.DiscountPercentage >= minDiscountPercentage);
            }

            // Trier selon le critère sélectionné
            promotionProducts = sortBy switch
            {
                "discount-asc" => promotionProducts.OrderBy(p => p.DiscountPercentage),
                "discount-desc" => promotionProducts.OrderByDescending(p => p.DiscountPercentage),
                "price-asc" => promotionProducts.OrderBy(p => p.Price),
                "price-desc" => promotionProducts.OrderByDescending(p => p.Price),
                "name" => promotionProducts.OrderBy(p => p.Name),
                _ => promotionProducts.OrderByDescending(p => p.DiscountPercentage)
            };

            totalItems = promotionProducts.Count();
            totalPages = (int)Math.Ceiling((double)totalItems / pageSize);
            
            // Pagination
            products = promotionProducts
                .Skip((currentPage - 1) * pageSize)
                .Take(pageSize)
                .ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading products: {ex.Message}");
            products = new List<ProductDto>();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task OnCategoryChanged(ChangeEventArgs e)
    {
        selectedCategoryId = string.IsNullOrEmpty(e.Value?.ToString()) ? null : int.Parse(e.Value.ToString()!);
        currentPage = 1;
        await LoadProducts();
    }

    private async Task OnDiscountChanged(ChangeEventArgs e)
    {
        minDiscountPercentage = int.Parse(e.Value?.ToString() ?? "0");
        currentPage = 1;
        await LoadProducts();
    }

    private async Task OnSortChanged(ChangeEventArgs e)
    {
        sortBy = e.Value?.ToString() ?? "discount-desc";
        currentPage = 1;
        await LoadProducts();
    }

    private async Task OnPageSizeChanged(ChangeEventArgs e)
    {
        pageSize = int.Parse(e.Value?.ToString() ?? "12");
        currentPage = 1;
        await LoadProducts();
    }

    private async Task ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            await LoadProducts();
        }
    }

    private async Task AddToCart(int productId)
    {
        Console.WriteLine($"🔍 DEBUG Promotions: Tentative d'ajout au panier - UserId: {_userId}, ProductId: {productId}");

        string userId;
        if (string.IsNullOrEmpty(_userId))
        {
            Console.WriteLine("🔍 DEBUG Promotions: Utilisateur non connecté, utilisation d'un ID invité");
            userId = await GetOrCreateGuestUserId();
        }
        else
        {
            userId = _userId;
        }

        try
        {
            Console.WriteLine($"🛒 DEBUG Promotions: Création de l'item panier - ProductId: {productId}, Quantity: 1");
            var cartItem = new CartItemCreateDto { ProductId = productId, Quantity = 1 };

            Console.WriteLine($"📡 DEBUG Promotions: Appel API AddItemToCartAsync...");
            var result = await CartService.AddItemToCartAsync(userId, cartItem);

            Console.WriteLine($"✅ DEBUG Promotions: Produit ajouté avec succès - ItemCount: {result?.ItemCount ?? 0}");

            // Notification de succès
            await JSRuntime.InvokeVoidAsync("showToast", $"✅ Produit ajouté au panier !", "success");
            Console.WriteLine($"Produit {productId} ajouté au panier.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ DEBUG Promotions: Erreur lors de l'ajout au panier: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("showToast", $"❌ Erreur: {ex.Message}", "error");
        }
    }

    private async Task<string> GetOrCreateGuestUserId()
    {
        // Utiliser le localStorage pour stocker l'ID de session invité
        var guestId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");

        if (string.IsNullOrEmpty(guestId))
        {
            guestId = $"guest_{Guid.NewGuid():N}";
            await JSRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", guestId);
        }

        return guestId;
    }
}
