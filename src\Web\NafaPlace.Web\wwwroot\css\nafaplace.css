/* Couleurs personnalisées */
:root {
    --primary: #e63946;
    --secondary: #457b9d;
    --dark: #1d3557;
    --light: #f1faee;
    --accent: #fca311;
}

/* Styles généraux */
body {
    font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--dark);
    background-color: #f8f9fa;
}

/* Header & Navigation */
.navbar {
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.6rem;
    color: var(--primary) !important;
}

.navbar-light .navbar-nav .nav-link {
    color: var(--dark);
    font-weight: 500;
    padding: 0.6rem 1rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.navbar-light .navbar-nav .nav-link:hover {
    color: var(--primary);
    background-color: rgba(230, 57, 70, 0.05);
}

.navbar-light .navbar-nav .nav-link.active {
    color: var(--primary);
    font-weight: 600;
}

.top-bar {
    background-color: var(--dark);
    color: white;
    font-size: 0.9rem;
    padding: 0.5rem 0;
}

.top-bar a {
    color: rgba(255, 255, 255, 0.8);
    transition: color 0.3s ease;
}

.top-bar a:hover {
    color: white;
    text-decoration: none;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.dropdown-item {
    padding: 0.6rem 1.2rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: rgba(230, 57, 70, 0.05);
    color: var(--primary);
}

.dropdown-item i {
    width: 20px;
    text-align: center;
}

/* Barre de recherche */
.search-form .input-group {
    border-radius: 50px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.search-form .form-control {
    border-radius: 50px 0 0 50px;
    border: 1px solid #ced4da;
    padding-left: 1.2rem;
}

.search-form .btn {
    border-radius: 0 50px 50px 0;
    padding: 0.375rem 1.2rem;
}

/* Icônes de navigation */
.nav-icon {
    position: relative;
    font-size: 1.3rem;
    color: var(--dark);
    margin: 0 0.8rem;
    transition: all 0.3s ease;
}

.nav-icon:hover {
    color: var(--primary);
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--primary);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Hero section */
.hero {
    background: linear-gradient(rgba(29, 53, 87, 0.8), rgba(29, 53, 87, 0.6)), url('/images/hero-bg.jpg') no-repeat center center;
    background-size: cover;
    color: white;
    padding: 6rem 0;
    margin-bottom: 3rem;
    border-radius: 0 0 30px 30px;
}

.hero h1 {
    font-weight: 700;
    font-size: 3rem;
    margin-bottom: 1.5rem;
    border: none;
    box-shadow: none;
    background: none;
    padding: 0;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero .btn {
    padding: 0.8rem 2rem;
    font-weight: 600;
    border-radius: 50px;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Catégories */
.category-card {
    transition: transform 0.3s, box-shadow 0.3s;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    cursor: pointer;
    border: none;
    margin-bottom: 1.5rem;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.12);
}

.category-card img {
    height: 180px;
    object-fit: cover;
    transition: transform 0.5s;
}

.category-card:hover img {
    transform: scale(1.05);
}

.category-card .card-body {
    padding: 1.2rem;
    text-align: center;
}

.category-card .card-title {
    font-weight: 600;
    margin-bottom: 0;
    color: var(--dark);
}

/* Footer */
footer {
    background-color: var(--dark);
    color: white;
    border-radius: 30px 30px 0 0;
    position: relative;
    z-index: 1;
}

footer a {
    color: rgba(255, 255, 255, 0.7);
    transition: color 0.3s ease;
}

footer a:hover {
    color: white;
    text-decoration: none;
}

footer h5 {
    font-weight: 600;
    margin-bottom: 1.5rem;
}

footer ul li {
    margin-bottom: 0.8rem;
}

/* Product Cards */
.product-card {
    border: none;
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    height: 100%;
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.12);
}

.product-card .card-img-top {
    height: 180px;
    object-fit: cover;
    transition: transform 0.5s;
}

.product-card:hover .card-img-top {
    transform: scale(1.05);
}

.product-card .card-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    line-height: 1.3;
    height: 2.6em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-price {
    font-weight: 700;
    font-size: 1.2rem;
    color: var(--primary);
}

.product-old-price {
    text-decoration: line-through;
    color: #6c757d;
    font-size: 0.9rem;
}

.badge-sale {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 10;
    background-color: var(--primary);
    color: white;
    font-weight: 600;
}

/* Modern Product Grid - Exactement 4 colonnes comme l'image de référence */
.product-grid-modern {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin: 0;
}

@media (max-width: 768px) {
    .product-grid-modern {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.8rem;
    }
}

/* Badge styles modernes */
.badge-discount {
    position: absolute;
    top: 8px;
    left: 8px;
    background: linear-gradient(135deg, #ff4757, #ff3742);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(255, 71, 87, 0.3);
}

.badge-new {
    position: absolute;
    top: 8px;
    right: 8px;
    background: linear-gradient(135deg, #2ed573, #1dd1a1);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(46, 213, 115, 0.3);
}

/* Prix modernes */
.current-price {
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--primary);
}

.old-price {
    text-decoration: line-through;
    color: #999;
    font-size: 0.9rem;
    margin-left: 8px;
}

.price-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

/* Boutons d'action */
.btn-add-cart {
    background: linear-gradient(135deg, var(--primary), #d63031);
    border: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-add-cart:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(230, 57, 70, 0.3);
}

/* Responsive */
@media (max-width: 1200px) {
    .product-grid-modern {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1.2rem;
    }
}

@media (max-width: 992px) {
    .navbar-collapse {
        background-color: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-top: 1rem;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .search-form {
        width: 100%;
        margin-bottom: 1rem;
    }

    .navbar-nav {
        margin-bottom: 1rem;
    }

    .product-grid-modern {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .hero {
        padding: 4rem 0;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .category-card img {
        height: 150px;
    }

    .product-card .card-img-top {
        height: 160px;
    }

    .product-grid-modern {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 0.8rem;
    }

    .product-card .card-title {
        font-size: 0.95rem;
    }

    .current-price {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .product-grid-modern {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .product-card .card-img-top {
        height: 140px;
    }

    .btn-add-cart {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
}

/* Ajout de styles pour corriger les problèmes de navbar */
.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.navbar-collapse {
    display: flex;
    align-items: center;
}

/* Correction pour le panier et les boutons de connexion/inscription */
.d-flex.align-items-center {
    margin-left: auto;
}

/* Style pour le titre principal sans encadrement */
.display-4.fw-bold {
    border: none;
    box-shadow: none;
    background: none;
    padding: 0;
}

/* Promotional Banner */
.promo-banner {
    background: linear-gradient(135deg, #FF6B35 0%, #F59E0B 100%);
    color: white;
    padding: 40px 0;
    margin: 60px 0;
    position: relative;
    overflow: hidden;
    border-radius: 15px;
}

.promo-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.promo-banner h3 {
    font-weight: 800;
    margin-bottom: 15px;
    font-size: 1.8rem;
    position: relative;
    z-index: 2;
}

.promo-banner p {
    margin-bottom: 20px;
    opacity: 0.95;
    font-size: 1.1rem;
    position: relative;
    z-index: 2;
}

.promo-banner .btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.8);
    color: white;
    padding: 12px 24px;
    font-weight: 600;
    border-radius: 25px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 2;
}

.promo-banner .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: white;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 255, 255, 0.3);
}

.promo-banner .bi-truck {
    color: rgba(255, 255, 255, 0.3);
    font-size: 4rem !important;
}

/* Responsive pour la bannière promotionnelle */
@media (max-width: 768px) {
    .promo-banner {
        padding: 30px 0;
        margin: 40px 0;
    }

    .promo-banner h3 {
        font-size: 1.5rem;
    }

    .promo-banner p {
        font-size: 1rem;
    }

    .promo-banner .bi-truck {
        font-size: 3rem !important;
    }
}
