/* Couleurs personnalisées */
:root {
    --primary: #e63946;
    --secondary: #457b9d;
    --dark: #1d3557;
    --light: #f1faee;
    --accent: #fca311;
}

/* Styles généraux */
body {
    font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--dark);
    background-color: #f8f9fa;
}

/* Header & Navigation */
.navbar {
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.6rem;
    color: var(--primary) !important;
}

.navbar-light .navbar-nav .nav-link {
    color: var(--dark);
    font-weight: 500;
    padding: 0.6rem 1rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.navbar-light .navbar-nav .nav-link:hover {
    color: var(--primary);
    background-color: rgba(230, 57, 70, 0.05);
}

.navbar-light .navbar-nav .nav-link.active {
    color: var(--primary);
    font-weight: 600;
}

.top-bar {
    background-color: var(--dark);
    color: white;
    font-size: 0.9rem;
    padding: 0.5rem 0;
}

.top-bar a {
    color: rgba(255, 255, 255, 0.8);
    transition: color 0.3s ease;
}

.top-bar a:hover {
    color: white;
    text-decoration: none;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.dropdown-item {
    padding: 0.6rem 1.2rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: rgba(230, 57, 70, 0.05);
    color: var(--primary);
}

.dropdown-item i {
    width: 20px;
    text-align: center;
}

/* Barre de recherche */
.search-form .input-group {
    border-radius: 50px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.search-form .form-control {
    border-radius: 50px 0 0 50px;
    border: 1px solid #ced4da;
    padding-left: 1.2rem;
}

.search-form .btn {
    border-radius: 0 50px 50px 0;
    padding: 0.375rem 1.2rem;
}

/* Icônes de navigation */
.nav-icon {
    position: relative;
    font-size: 1.3rem;
    color: var(--dark);
    margin: 0 0.8rem;
    transition: all 0.3s ease;
}

.nav-icon:hover {
    color: var(--primary);
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--primary);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Hero section */
.hero {
    background: linear-gradient(rgba(29, 53, 87, 0.8), rgba(29, 53, 87, 0.6)), url('/images/hero-bg.jpg') no-repeat center center;
    background-size: cover;
    color: white;
    padding: 6rem 0;
    margin-bottom: 3rem;
    border-radius: 0 0 30px 30px;
}

.hero h1 {
    font-weight: 700;
    font-size: 3rem;
    margin-bottom: 1.5rem;
    border: none;
    box-shadow: none;
    background: none;
    padding: 0;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero .btn {
    padding: 0.8rem 2rem;
    font-weight: 600;
    border-radius: 50px;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Catégories */
.category-card {
    transition: transform 0.3s, box-shadow 0.3s;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    cursor: pointer;
    border: none;
    margin-bottom: 1.5rem;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.12);
}

.category-card img {
    height: 180px;
    object-fit: cover;
    transition: transform 0.5s;
}

.category-card:hover img {
    transform: scale(1.05);
}

.category-card .card-body {
    padding: 1.2rem;
    text-align: center;
}

.category-card .card-title {
    font-weight: 600;
    margin-bottom: 0;
    color: var(--dark);
}

/* Footer */
footer {
    background-color: var(--dark);
    color: white;
    border-radius: 30px 30px 0 0;
    position: relative;
    z-index: 1;
}

footer a {
    color: rgba(255, 255, 255, 0.7);
    transition: color 0.3s ease;
}

footer a:hover {
    color: white;
    text-decoration: none;
}

footer h5 {
    font-weight: 600;
    margin-bottom: 1.5rem;
}

footer ul li {
    margin-bottom: 0.8rem;
}

/* Product Cards */
.product-card {
    border: none;
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    height: 100%;
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.12);
}

.product-card .card-img-top {
    height: 180px;
    object-fit: cover;
    transition: transform 0.5s;
}

.product-card:hover .card-img-top {
    transform: scale(1.05);
}

.product-card .card-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    line-height: 1.3;
    height: 2.6em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-price {
    font-weight: 700;
    font-size: 1.2rem;
    color: var(--primary);
}

.product-old-price {
    text-decoration: line-through;
    color: #6c757d;
    font-size: 0.9rem;
}

.badge-sale {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 10;
    background-color: var(--primary);
    color: white;
    font-weight: 600;
}

/* Modern Product Grid - Exactement 4 colonnes comme l'image de référence */
.product-grid-modern {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin: 0;
}

@media (max-width: 768px) {
    .product-grid-modern {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.8rem;
    }
}

/* Badge styles modernes */
.badge-discount {
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #ff4757, #ff3742);
    color: white;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(255, 71, 87, 0.3);
}

.badge-new {
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #2ed573, #1dd1a1);
    color: white;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(46, 213, 115, 0.3);
}

.badge-sale {
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #ff4757, #ff3742);
    color: white;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(255, 71, 87, 0.3);
}

/* Prix modernes */
.current-price {
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--primary);
}

.old-price {
    text-decoration: line-through;
    color: #999;
    font-size: 0.9rem;
    margin-left: 8px;
}

.price-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

/* Boutons d'action */
.btn-add-cart {
    background: linear-gradient(135deg, var(--primary), #d63031);
    border: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-add-cart:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(230, 57, 70, 0.3);
}

/* Responsive */
@media (max-width: 1200px) {
    .product-grid-modern {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1.2rem;
    }
}

@media (max-width: 992px) {
    .navbar-collapse {
        background-color: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-top: 1rem;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .search-form {
        width: 100%;
        margin-bottom: 1rem;
    }

    .navbar-nav {
        margin-bottom: 1rem;
    }

    .product-grid-modern {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .hero {
        padding: 4rem 0;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .category-card img {
        height: 150px;
    }

    .product-card .card-img-top {
        height: 160px;
    }

    .product-grid-modern {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 0.8rem;
    }

    .product-card .card-title {
        font-size: 0.95rem;
    }

    .current-price {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .product-grid-modern {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .product-card .card-img-top {
        height: 140px;
    }

    .btn-add-cart {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
}

/* Ajout de styles pour corriger les problèmes de navbar */
.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.navbar-collapse {
    display: flex;
    align-items: center;
}

/* Correction pour le panier et les boutons de connexion/inscription */
.d-flex.align-items-center {
    margin-left: auto;
}

/* Style pour le titre principal sans encadrement */
.display-4.fw-bold {
    border: none;
    box-shadow: none;
    background: none;
    padding: 0;
}

/* Promotional Banner */
.promo-banner {
    background: linear-gradient(135deg, #FF6B35 0%, #F59E0B 100%);
    color: white;
    padding: 40px 0;
    margin: 60px 0;
    position: relative;
    overflow: hidden;
    border-radius: 15px;
}

.promo-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    /* Animation désactivée pour éviter le clignotement */
    /* animation: shimmer 3s infinite; */
}

/* Animation shimmer désactivée pour éviter les problèmes de performance
@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
} */

.promo-banner h3 {
    font-weight: 800;
    margin-bottom: 15px;
    font-size: 1.8rem;
    position: relative;
    z-index: 2;
}

.promo-banner p {
    margin-bottom: 20px;
    opacity: 0.95;
    font-size: 1.1rem;
    position: relative;
    z-index: 2;
}

.promo-banner .btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.8);
    color: white;
    padding: 12px 24px;
    font-weight: 600;
    border-radius: 25px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 2;
}

.promo-banner .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: white;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 255, 255, 0.3);
}

.promo-banner .bi-truck {
    color: rgba(255, 255, 255, 0.3);
    font-size: 4rem !important;
}

/* Responsive pour la bannière promotionnelle */
@media (max-width: 768px) {
    .promo-banner {
        padding: 30px 0;
        margin: 40px 0;
    }

    .promo-banner h3 {
        font-size: 1.5rem;
    }

    .promo-banner p {
        font-size: 1rem;
    }

    .promo-banner .bi-truck {
        font-size: 3rem !important;
    }
}

/* Styles pour les nouvelles pages */
.page-header {
    text-align: center;
    margin-bottom: 2rem;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark);
    margin-bottom: 0.5rem;
}

.page-subtitle {
    font-size: 1.1rem;
    color: var(--gray);
    margin-bottom: 0;
}

.filters-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.empty-state {
    padding: 3rem 1rem;
}

.empty-state i {
    opacity: 0.3;
}

/* Badge styles pour les classements */
.badge-ranking {
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.badge-ranking.position-1 {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
}

.badge-ranking.position-2 {
    background: linear-gradient(135deg, #c0c0c0, #e5e5e5);
    color: #333;
}

.badge-ranking.position-3 {
    background: linear-gradient(135deg, #cd7f32, #daa520);
    color: white;
}

.badge-bestseller {
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 10;
}

/* Stats cards */
.promotion-stats, .bestsellers-stats {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.stat-card {
    padding: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--gray);
    font-size: 0.9rem;
    margin-bottom: 0;
}

.savings-amount {
    margin-top: 0.5rem;
}

/* Blog styles */
.featured-article {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    color: white;
    margin-bottom: 3rem;
    position: relative;
    overflow: hidden;
    max-width: 100%;
}

.featured-article::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.featured-article .article-content {
    position: relative;
    z-index: 2;
}

.article-title {
    font-size: 2.2rem;
    font-weight: 800;
    color: white;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.article-excerpt {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.7;
    margin-bottom: 2rem;
}

.article-meta {
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.8);
}

.blog-categories {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 3rem;
}

.blog-categories h5 {
    color: var(--dark);
    font-weight: 700;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.category-tags .btn {
    margin-right: 0.75rem;
    margin-bottom: 0.75rem;
    border-radius: 25px;
    padding: 0.6rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.category-tags .btn-outline-primary {
    border-color: var(--primary);
    color: var(--primary);
}

.category-tags .btn-outline-primary:hover,
.category-tags .btn-outline-primary.active {
    background: var(--primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(231, 60, 48, 0.3);
}

.blog-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: none;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    height: 100%;
    max-width: 100%;
    word-wrap: break-word;
}

.blog-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.blog-card .card-img-top {
    height: 200px;
    object-fit: cover;
    transition: transform 0.2s ease;
}

.blog-card:hover .card-img-top {
    transform: scale(1.02);
}

.blog-card .card-body {
    padding: 1.5rem;
}

.blog-card .card-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--dark);
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.blog-card .card-text {
    color: var(--gray);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.article-category .badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-weight: 600;
}

.blog-card .card-footer {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
}

.blog-card .btn-outline-primary {
    border-radius: 20px;
    padding: 0.4rem 1rem;
    font-size: 0.85rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.blog-card .btn-outline-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(231, 60, 48, 0.2);
}

.newsletter-section {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
    border-radius: 12px;
    padding: 2rem;
}

/* Prévention des débordements pour le blog */
.blog-categories,
.featured-article,
.blog-card,
.newsletter-section {
    box-sizing: border-box;
    max-width: 100%;
}

.blog-categories .category-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.blog-categories .category-tags .btn {
    flex-shrink: 0;
}

/* Stabilité de la page */
body {
    overflow-x: hidden;
}

.content {
    max-width: 100%;
    overflow-x: hidden;
}

/* Optimisations pour éviter le clignotement */
* {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

.blog-card,
.featured-article,
.category-tags .btn {
    will-change: transform;
}

/* Réduction des transitions agressives */
.blog-card:hover,
.product-card:hover,
.category-card:hover {
    transition-duration: 0.2s;
}

/* Contact styles */
.contact-form-card, .contact-info-card, .social-media-card, .faq-card, .map-card {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
}

.contact-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(231, 60, 48, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.contact-icon i {
    font-size: 1.2rem;
}

.contact-details h6 {
    font-weight: 600;
    color: var(--dark);
    margin-bottom: 0.25rem;
}

.contact-details p {
    color: var(--gray);
    font-size: 0.9rem;
}

.social-links {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.social-link {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
    color: var(--dark);
}

.social-link:hover {
    color: white;
    transform: translateX(5px);
}

.social-link i {
    width: 24px;
    margin-right: 0.75rem;
    font-size: 1.2rem;
}

.social-link.facebook:hover { background: #1877f2; }
.social-link.twitter:hover { background: #1da1f2; }
.social-link.instagram:hover { background: #e4405f; }
.social-link.linkedin:hover { background: #0077b5; }
.social-link.whatsapp:hover { background: #25d366; }

.map-placeholder {
    height: 300px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    border-radius: 0 0 12px 12px;
}

.map-content {
    padding: 2rem;
}

/* Nouveautés, Promotions, Meilleures Ventes styles améliorés */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    margin-bottom: 3rem;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    pointer-events: none;
}

.page-header .container {
    position: relative;
    z-index: 2;
}

.page-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.page-subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
    font-weight: 300;
}

.filters-section {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 3rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.filters-section h5 {
    color: var(--dark);
    font-weight: 700;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.filters-section .form-select,
.filters-section .form-control {
    border-radius: 12px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.filters-section .form-select:focus,
.filters-section .form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(231, 60, 48, 0.1);
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.product-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.4s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.product-image {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.product-card:hover .product-image {
    transform: scale(1.08);
}

.product-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 2;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.savings-amount {
    font-size: 0.85rem;
    font-weight: 500;
}

.product-info {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.product-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--dark);
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.product-category {
    color: var(--gray);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.price-section {
    margin-top: auto;
    margin-bottom: 1.5rem;
}

.price-with-discount {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.current-price {
    font-size: 1.4rem;
    font-weight: 800;
    color: var(--primary);
}

.original-price {
    font-size: 1rem;
    color: var(--gray);
    text-decoration: line-through;
    font-weight: 500;
}

.discount-badge {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.new-badge {
    background: linear-gradient(135deg, var(--secondary), #ff6b35);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    position: absolute;
    top: 1rem;
    left: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 2;
}

.bestseller-badge {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    position: absolute;
    top: 1rem;
    left: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 2;
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.card-actions {
    margin-top: auto;
}

.btn-add-cart {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    border: none;
    color: white;
    padding: 0.875rem 1.5rem;
    border-radius: 15px;
    font-weight: 700;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    width: 100%;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.btn-add-cart::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-add-cart:hover::before {
    left: 100%;
}

.btn-add-cart:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(231, 60, 48, 0.4);
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--gray);
    font-size: 1rem;
    font-weight: 600;
}

/* Pagination améliorée */
.pagination .page-link {
    border-radius: 12px;
    margin: 0 0.25rem;
    border: 2px solid transparent;
    color: var(--dark);
    font-weight: 600;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    border-color: var(--primary);
    color: white;
    box-shadow: 0 4px 15px rgba(231, 60, 48, 0.3);
}

.pagination .page-link:hover {
    background: var(--primary);
    border-color: var(--primary);
    color: white;
    transform: translateY(-2px);
}
