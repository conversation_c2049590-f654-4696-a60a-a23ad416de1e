@page "/catalog/featured"
@using NafaPlace.Web.Services
@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Components.Reviews
@inject IProductService ProductService
@inject ICategoryService CategoryService
@inject ICartService CartService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@using System.Security.Claims
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>Produits en vedette - NafaPlace</PageTitle>

<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Accueil</a></li>
                    <li class="breadcrumb-item"><a href="/catalog">Catalogue</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Produits en vedette</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center mb-3">
                <i class="bi bi-star-fill text-warning me-2" style="font-size: 2rem;"></i>
                <h1 class="mb-0">Produits en vedette</h1>
            </div>
            <p class="text-muted">Découvrez notre sélection de produits exceptionnels, choisis spécialement pour vous.</p>
        </div>
    </div>

    @if (loading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-2">Chargement des produits en vedette...</p>
        </div>
    }
    else if (products?.Any() == true)
    {
        <div class="row">
            @foreach (var product in products)
            {
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="product-card h-100">
                        <div class="position-relative">
                            <img src="@GetProductImageUrl(product.ImageUrl)" 
                                 class="product-image" 
                                 alt="@product.Name"
                                 loading="lazy" />
                            <div class="product-badge bg-warning text-dark">
                                <i class="bi bi-star-fill me-1"></i>Vedette
                            </div>
                        </div>
                        
                        <div class="product-info">
                            <h5 class="product-title">@product.Name</h5>
                            <p class="product-description">@product.Description</p>
                            
                            <div class="product-rating mb-2">
                                <ReviewStars Rating="@product.AverageRating" />
                                <span class="text-muted ms-2">(@product.ReviewCount avis)</span>
                            </div>
                            
                            <div class="product-price">
                                @if (product.DiscountPrice.HasValue && product.DiscountPrice < product.Price)
                                {
                                    <span class="price-original">@product.Price.ToString("N0") GNF</span>
                                    <span class="price-discount">@product.DiscountPrice.Value.ToString("N0") GNF</span>
                                }
                                else
                                {
                                    <span class="price-current">@product.Price.ToString("N0") GNF</span>
                                }
                            </div>
                            
                            <div class="product-actions mt-3">
                                <button class="btn btn-primary btn-add-cart w-100" 
                                        @onclick="() => ViewProduct(product.Id)"
                                        disabled="@(product.Stock <= 0)">
                                    @if (product.Stock <= 0)
                                    {
                                        <i class="bi bi-x-circle me-2"></i>Rupture de stock
                                    }
                                    else
                                    {
                                        <i class="bi bi-eye me-2"></i>Voir le produit
                                    }
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="bi bi-star text-muted" style="font-size: 4rem;"></i>
            <h3 class="mt-3 text-muted">Aucun produit en vedette</h3>
            <p class="text-muted">Revenez bientôt pour découvrir nos nouveaux produits vedettes !</p>
            <a href="/catalog" class="btn btn-primary">
                <i class="bi bi-box-seam me-2"></i>Voir tous les produits
            </a>
        </div>
    }
</div>

@code {
    private List<ProductDto> products = new();
    private bool loading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadFeaturedProducts();
    }

    private async Task LoadFeaturedProducts()
    {
        try
        {
            loading = true;
            products = await ProductService.GetFeaturedProductsAsync(20);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading featured products: {ex.Message}");
        }
        finally
        {
            loading = false;
        }
    }

    private string GetProductImageUrl(string imageUrl)
    {
        if (string.IsNullOrEmpty(imageUrl))
            return "/images/placeholder.png";
        
        if (imageUrl.StartsWith("http"))
            return imageUrl;
        
        return $"/images/{imageUrl}";
    }

    private void ViewProduct(int productId)
    {
        NavigationManager.NavigateTo($"/catalog/product/{productId}");
    }
}
